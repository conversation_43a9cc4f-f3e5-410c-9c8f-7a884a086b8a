import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'ObsidianClipper',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#f8f8f8',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  tabBar: {
    color: '#8E8E93',
    selectedColor: '#666',
    backgroundColor: '#FFF',
    borderStyle: 'black',
    height: '56px',
    fontSize: '12px',
    iconWidth: '28px',
    spacing: '4px',
    list: [
      {
        iconPath: 'static/tabbar/operate.png',
        selectedIconPath: 'static/tabbar/operateHL.png',
        pagePath: 'pages/notion/notion',
        text: '操作',
      },
      {
        iconPath: 'static/tabbar/home.png',
        selectedIconPath: 'static/tabbar/homeHL.png',
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        iconPath: 'static/tabbar/personal.png',
        selectedIconPath: 'static/tabbar/personalHL.png',
        pagePath: 'pages/about/about',
        text: '我的',
      },
    ],
  },
})
