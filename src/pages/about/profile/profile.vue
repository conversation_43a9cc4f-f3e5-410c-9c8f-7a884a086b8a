<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '自定义配置',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view
    class="overflow-hidden flex flex-col items-center justify-start w-full h-100% m-0 bg-#f8f8f7 pb-safe"
  >
    <view class="profile-container">
      <!-- 当数据加载中时显示加载提示 -->
      <view v-if="loading && !configLoaded" class="loading-container">
        <wd-loading size="40px" />
        <view class="loading-text">加载配置中...</view>
      </view>

      <wd-form
        :model="formData"
        ref="formRef"
        label-align="right"
        v-else
        custom-class="form-custom-width"
      >
        <wd-card custom-class="tr-shadow rounded-12px card-width !mx-0">
          <view class="settings-group">
            <view class="settings-group-title font-medium text-gray-700 mb-2">图片设置</view>
            <wd-form-item label="剪藏时存储图片到图床" label-width="400rpx">
              <wd-switch
                v-model="formData.alwaysUsePicCloud"
                size="20px"
                :disabled="!configLoaded || switchLoading"
                @change="(value) => handleSwitchChange('alwaysUsePicCloud', value)"
              />
            </wd-form-item>
            <wd-form-item label="优先使用Cloudinary存储图片" label-width="400rpx">
              <wd-switch
                v-model="formData.alwaysUseCloudinary"
                size="20px"
                :disabled="!configLoaded || switchLoading"
                @change="(value) => handleSwitchChange('alwaysUseCloudinary', value)"
              />
            </wd-form-item>
          </view>

          <view class="settings-group mt-4">
            <view class="settings-group-title font-medium text-gray-700 mb-2">Obsidian设置</view>
            <wd-form-item label="(仅)保存到Obsidian" label-width="400rpx">
              <wd-switch
                v-model="formData.toObsidian"
                size="20px"
                :disabled="!configLoaded || switchLoading"
                @change="(value) => handleSwitchChange('toObsidian', value)"
              />
            </wd-form-item>
          </view>

          <view class="settings-group mt-4">
            <view class="settings-group-title font-medium text-gray-700 mb-2">页面设置</view>
            <wd-form-item label="启用通用页面剪藏" label-width="400rpx">
              <wd-switch
                v-model="formData.supportAllSite"
                size="20px"
                :disabled="!configLoaded || switchLoading"
                @change="(value) => handleSwitchChange('supportAllSite', value)"
              />
            </wd-form-item>
            <wd-form-item label="不要设置页面icon" label-width="400rpx">
              <wd-switch
                v-model="formData.noIcon"
                size="20px"
                :disabled="!configLoaded || switchLoading"
                @change="(value) => handleSwitchChange('noIcon', value)"
              />
            </wd-form-item>
            <wd-form-item label="不要设置页面头图" label-width="400rpx">
              <wd-switch
                v-model="formData.noCover"
                size="20px"
                :disabled="!configLoaded || switchLoading"
                @change="(value) => handleSwitchChange('noCover', value)"
              />
            </wd-form-item>
          </view>
          <view class="settings-group mt-4">
            <view class="settings-group-title font-medium text-gray-700 mb-2">小程序设置</view>
            <wd-form-item label="设置入口页为快捷剪藏页" label-width="400rpx">
              <wd-switch
                v-model="formData.quickClipAsEnterPage"
                size="20px"
                :disabled="!configLoaded || switchLoading"
                @change="(value) => handleSwitchChange('quickClipAsEnterPage', value)"
              />
            </wd-form-item>
          </view>
        </wd-card>
      </wd-form>
    </view>

    <!-- 只保留初始化按钮 -->
    <view class="form-footer">
      <wd-button
        type="info"
        @click="handleRemove"
        custom-class="tr-shadow btn-reset"
        :loading="removeLoading"
        :disabled="!configLoaded || switchLoading"
      >
        重置为默认配置
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useToast } from 'wot-design-uni'
import { getCustomConfig, postCustomConfig, type CustomConfig } from '@/service/user/config'

const toast = useToast()
const formRef = ref()
const loading = ref(false)
const removeLoading = ref(false)
const configLoaded = ref(false)
// 新增：开关操作的loading状态
const switchLoading = ref(false)

// 表单数据，初始值为 undefined，必须从服务端获取
const formData = reactive({
  alwaysUsePicCloud: undefined as boolean | undefined,
  alwaysUseCloudinary: undefined as boolean | undefined,
  supportAllSite: undefined as boolean | undefined,
  noIcon: undefined as boolean | undefined,
  noCover: undefined as boolean | undefined,
  quickClipAsEnterPage: undefined as boolean | undefined,
  toObsidian: undefined as boolean | undefined,
})
// 防抖函数实现
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number) => {
  let timeoutId: number | null = null
  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => fn.apply(null, args), delay)
  }
}

// 获取用户配置
const fetchConfig = async () => {
  loading.value = true
  try {
    const { data } = await getCustomConfig()
    if (data) {
      // 直接使用服务端返回的数据，不使用默认值
      formData.alwaysUsePicCloud = data.alwaysUsePicCloud
      formData.alwaysUseCloudinary = data.alwaysUseCloudinary
      formData.supportAllSite = data.supportAllSite
      formData.noIcon = data.noIcon
      formData.noCover = data.noCover
      formData.quickClipAsEnterPage = data.quickClipAsEnterPage
      formData.toObsidian = data.toObsidian

      configLoaded.value = true
      console.log('配置加载成功:', data)
    } else {
      toast.show({
        msg: '获取配置失败',
        iconClass: 'error-circle-filled',
      })
    }
  } catch (error) {
    console.error('获取配置失败:', error)
    toast.show({
      msg: '获取配置失败，请重试',
      iconClass: 'error-circle-filled',
    })
  } finally {
    loading.value = false
  }
}

// 单个配置项更新函数
const updateSingleConfig = async (field: keyof CustomConfig, value: boolean) => {
  if (!configLoaded.value || switchLoading.value) {
    return
  }

  // 记录原始值，用于失败时回滚
  const originalValue = formData[field]

  switchLoading.value = true
  console.log(`开始更新配置项 ${field}:`, value)

  try {
    // 构建完整的配置对象
    const configData: CustomConfig = {
      alwaysUsePicCloud: formData.alwaysUsePicCloud,
      alwaysUseCloudinary: formData.alwaysUseCloudinary,
      supportAllSite: formData.supportAllSite,
      noIcon: formData.noIcon,
      noCover: formData.noCover,
      quickClipAsEnterPage: formData.quickClipAsEnterPage,
      toObsidian: formData.toObsidian,
    }

    const { data } = await postCustomConfig(configData)

    if (data) {
      // 特殊处理：如果是快捷剪藏页设置，需要更新本地存储
      if (field === 'quickClipAsEnterPage') {
        uni.setStorageSync('quickClipAsEnterPage', value)
        console.log('已更新本地存储 quickClipAsEnterPage:', value)
      }

      toast.success(`${getFieldDisplayName(field)} 已${value ? '开启' : '关闭'}`)
      console.log(`配置项 ${field} 更新成功`)
    } else {
      // 请求成功但返回失败，回滚状态
      formData[field] = originalValue
      toast.error('保存失败，请重试')
      console.error(`配置项 ${field} 更新失败: 服务器返回失败`)
    }
  } catch (error) {
    // 请求失败，回滚状态
    formData[field] = originalValue
    console.error(`配置项 ${field} 更新失败:`, error)
    toast.error('网络错误，设置已恢复')
  } finally {
    switchLoading.value = false
  }
}

// 防抖处理的配置更新函数
const debouncedUpdateConfig = debounce(updateSingleConfig, 500)

// 开关变化处理函数
const handleSwitchChange = (field: keyof CustomConfig, value: boolean) => {
  console.log(`开关 ${field} 变化为:`, value)
  debouncedUpdateConfig(field, value)
}

// 获取字段显示名称
const getFieldDisplayName = (field: keyof CustomConfig): string => {
  const fieldNames: Record<keyof CustomConfig, string> = {
    alwaysUsePicCloud: '图片存储到图床',
    alwaysUseCloudinary: 'Cloudinary优先存储',
    supportAllSite: '通用页面剪藏',
    noIcon: '不设置页面icon',
    noCover: '不设置页面头图',
    quickClipAsEnterPage: '快捷剪藏为入口页',
    toObsidian: '仅保存到Obsidian',
  }
  return fieldNames[field] || field
}

// 重置为默认配置
const handleRemove = async () => {
  removeLoading.value = true
  try {
    // 创建默认配置
    const defaultConfig: CustomConfig = {
      alwaysUsePicCloud: true,
      alwaysUseCloudinary: true,
      supportAllSite: false,
      noIcon: false,
      noCover: false,
      quickClipAsEnterPage: false,
      toObsidian: false,
    }

    const { data } = await postCustomConfig(defaultConfig)

    if (data) {
      // 重置表单数据
      formData.alwaysUsePicCloud = true
      formData.alwaysUseCloudinary = true
      formData.supportAllSite = false
      formData.noIcon = false
      formData.noCover = false
      formData.quickClipAsEnterPage = false
      formData.toObsidian = false

      // 同时更新本地存储中的设置
      uni.setStorageSync('quickClipAsEnterPage', false)
      toast.success('已重置为默认配置')
      console.log('配置重置成功')
    } else {
      toast.error('重置失败')
      console.error('配置重置失败: 服务器返回失败')
    }
  } catch (error) {
    console.error('重置配置失败:', error)
    toast.error('重置失败，请重试')
  } finally {
    removeLoading.value = false
  }
}

// 页面加载时获取配置
onMounted(() => {
  fetchConfig()
})
</script>

<style lang="scss" scoped>
.profile-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-top: 12px;
  background-color: #f8f8f7;
}

:deep(.form-custom-width) {
  width: 690rpx;
}

.settings-group {
  padding: 0;

  &-title {
    padding-left: 10rpx;
    border-left: 6rpx solid #4080ff;
  }
}

.form-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 750rpx;
  margin-top: 12px;
}

:deep(.btn-reset) {
  width: 690rpx !important;
}

:deep(.wd-button) {
  height: 88rpx;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.wd-button--primary) {
  background-color: #337ea9;
  border-color: #337ea9;
}

:deep(.wd-button--primary:active) {
  background-color: #2a6a8f;
  border-color: #2a6a8f;
  transform: translateY(2rpx);
}

:deep(.wd-button--info) {
  color: #666;
  background-color: #f5f7fa;
  border-color: #ddd;
}

:deep(.wd-button--info:active) {
  color: #555;
  background-color: #e8e8e8;
  border-color: #ccc;
  transform: translateY(2rpx);
}

:deep(.wd-button.is-disabled) {
  cursor: not-allowed;
  opacity: 0.5;
}

:deep(.wd-button.is-disabled:active) {
  transform: none;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 690rpx;
  height: 80vh;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}
</style>
