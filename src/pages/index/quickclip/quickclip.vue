<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '快捷剪藏',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="clipper-container">
    <view class="clipper-card">
      <!-- 文本输入区域 -->
      <view class="input-area">
        <wd-textarea
          v-model="textContent"
          placeholder="请输入文本或链接内容..."
          :maxlength="2000"
          :rows="8"
          auto-focus
          custom-class="text-input"
          @input="handleTextChange"
        />
      </view>

      <!-- 底部按钮区域 -->
      <view class="bottom-buttons">
        <view class="button-left">
          <view class="clear-button" @click="clearContent">清空</view>
        </view>
        <view class="button-right">
          <view class="clipboard-button" @click="getClipboardContent">读取剪贴板</view>
          <view class="save-button" @click="handleSave">保存内容</view>
        </view>
      </view>
    </view>
    <wd-gap height="16px" />
    <wd-card v-if="extractedLink" custom-class="link-card !m-0">
      <template #title>
        <view class="card-title-container">
          <text class="card-title !text-size-16px !font-medium">提取到的链接：</text>
          <view class="parse-button !text-size-14px" @click="openExtractedLink">解析链接</view>
        </view>
      </template>
      <view class="link-content">
        <wd-text
          size="14px"
          color="#0066cc"
          :text="extractedLink"
          custom-class="link-text"
        ></wd-text>
      </view>
    </wd-card>
  </view>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import { useToast, useMessage } from 'wot-design-uni'
import { extractErrorMessage } from '@/utils'
import { saveMessage, MessageBody } from '@/service/clip/message'
import { resolveLink, UrlRQ } from '@/service/clip/article'
import { useUserStore } from '@/store'

// 初始化toast
const toast = useToast()
const message = useMessage()

// 获取用户store
const userStore = useUserStore()

// 检查是否已授权Notion
const isAuthorized = computed(() => !!userStore.userInfo.notionAuth)

// 跳转到授权页面
const navigateToAuthorize = () => {
  uni.navigateTo({
    url: '/pages/notion/authorize/authorize',
  })
}

// 文本内容
const textContent = ref('')

// 提取到的链接
const extractedLink = ref('')

const urlRegex = /(https?:\/\/[^\s]+)/g
const reg = /[\u4e00-\u9fa5]/g
const regSpace = /[ ].*/g
const regComma = /[，|,].*/g

// 解析文本中的链接
const extractLinks = (text: string) => {
  if (!text.trim()) return null

  // 提取URL的正则表达式
  const matches = text.match(urlRegex)
  if (matches && matches.length > 0) {
    const linkUrl = matches[0].replace(reg, '').replace(regComma, '').replace(regSpace, '')
    return linkUrl // 返回第一个链接
  }
  return null
}

// 处理文本变化
const handleTextChange = () => {
  console.log('文本变化:', textContent.value)
  const link = extractLinks(textContent.value)
  console.log('提取到的链接:', link)
  extractedLink.value = link || ''
}

// 读取剪贴板内容
const getClipboardContent = () => {
  uni.getClipboardData({
    success: (res) => {
      if (res.data) {
        textContent.value = res.data
        handleTextChange() // 自动解析剪贴板内容中的链接
      } else {
        toast.warning('剪贴板内容为空')
      }
    },
    fail: () => {},
  })
}

// 清空内容
const clearContent = () => {
  if (textContent.value) {
    textContent.value = ''
    extractedLink.value = '' // 同时清空提取的链接
  }
}

// 解析提取到的链接
const openExtractedLink = async () => {
  if (!extractedLink.value) {
    toast.warning('未提取到有效的链接')
    return
  }

  // 检查授权状态
  if (!isAuthorized.value) {
    uni.showModal({
      title: '授权提示',
      content: '请先完成Notion授权，才能解析链接',
      confirmText: '去授权',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 跳转到授权页面
          navigateToAuthorize()
        }
      },
    })
    return
  }

  try {
    toast.loading('解析链接中...')

    // 准备请求数据
    const linkData: UrlRQ = {
      url: extractedLink.value,
    }

    // 调用解析链接接口
    const { data, msg } = await resolveLink(linkData)

    // 关闭加载提示
    toast.close()

    if (data) {
      // 这里要判断如果解析的文章标题内容为空，则提示解析失败
      if (!data.title) {
        toast.error('解析失败: 请重试')
        return
      }
      // 解析成功，将解析结果转为 JSON 字符串并进行编码
      const articleDataStr = encodeURIComponent(JSON.stringify(data))

      // 跳转到文章页面并传递解析结果数据
      uni.navigateTo({
        url: `/pages/index/article/article?articleData=${articleDataStr}`,
        success: () => {
          console.log('跳转到文章页面成功')
          toast.success('解析成功')
        },
        fail: (err) => {
          console.error('跳转失败:', err)
          toast.error('跳转失败')
        },
      })
    } else {
      // 解析失败，显示错误信息
      toast.error(msg || '解析链接失败')
    }
  } catch (error) {
    // 使用全局错误提取方法
    const errorMessage = extractErrorMessage(error)

    // 关闭加载提示
    toast.close()

    // 显示错误提示
    toast.error('解析失败: ' + errorMessage)
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 保存内容到消息库
const handleSave = async () => {
  if (!textContent.value.trim()) {
    toast.warning('内容不能为空')
    return
  }

  // 检查授权状态
  if (!isAuthorized.value) {
    uni.showModal({
      title: '授权提示',
      content: '请先完成Notion授权，才能保存内容',
      confirmText: '去授权',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 跳转到授权页面
          navigateToAuthorize()
        }
      },
    })
    return
  }

  try {
    toast.loading('保存中...')

    // 准备请求数据
    const messageData: MessageBody = {
      content: textContent.value,
    }

    // 调用保存接口
    const { data, msg } = await saveMessage(messageData)

    // 关闭加载提示
    toast.close()

    if (data) {
      toast.success('内容已保存')
      // 可选：保存成功后清空内容
      clearContent()

      // 设置需要刷新剪藏数据的标记
      userStore.setNeedRefreshClipData(true)
    } else {
      const errMsg = msg
      message
        .confirm({
          title: '保存失败',
          msg: errMsg,
          confirmButtonText: 'OK',
          cancelButtonText: '去关联',
        })
        .then(() => {
          // 停留在当前页面
        })
        .catch(() => {
          // 点击去关联后跳转到关联页面
          uni.navigateTo({
            url: '/pages/notion/messagedatabase/messagedatabase',
          })
        })
    }
  } catch (error) {
    // 使用全局错误提取方法
    const errorMessage = extractErrorMessage(error)

    // 关闭加载提示
    toast.close()

    // 显示错误提示
    toast.error('保存失败: ' + errorMessage)
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 监听文本内容变化
watch(textContent, () => {
  handleTextChange()
})
</script>

<style lang="scss" scoped>
.clipper-container {
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f7f8fa;
}

.clipper-card {
  padding: 0;
  margin-bottom: 8rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.input-area {
  padding: 24rpx 24rpx 16rpx;
}

:deep(.text-input) {
  box-sizing: border-box !important;
  width: 100% !important;
  padding: 16rpx 24rpx;
  margin: 0;
  font-size: 15px !important;
  line-height: 1.5 !important;
  background-color: #fff;
  border: none;
  border-radius: 0;
}

:deep(.wd-textarea__inner) {
  box-sizing: border-box !important;
  width: 100% !important;
  min-height: 160rpx;
  max-height: 320rpx;
}

.bottom-buttons {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 24rpx;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.button-left,
.button-right {
  display: flex;
}

.clear-button,
.clipboard-button,
.save-button {
  padding: 8rpx 16rpx;
  font-size: 14px;
  color: #ff6b6b;
  background-color: transparent;
}

.clipboard-button {
  color: #3498db;
}

.save-button {
  margin-left: 16rpx;
  color: #3498db;
}

:deep(.link-card) {
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.link-content {
  padding: 16rpx 0;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

:deep(.link-text) {
  text-decoration: underline;
}

.button-row {
  display: flex;
  gap: 12rpx;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 16rpx;
}

.custom-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 400rpx;
  height: 72rpx;
  overflow: hidden;
  font-size: 14px;
  border-radius: 36rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.custom-button::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  content: '';
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.custom-button:active::after {
  opacity: 1;
}

.info-button {
  color: #666;
  background-color: #f5f7fa;
  border: 1px solid #ddd;
}

.info-button:active {
  color: #555;
  background-color: #e8e8e8;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  transform: translateY(2rpx);
}

.primary-button {
  color: #fff;
  background-color: #337ea9;
  border: 1px solid #337ea9;
  box-shadow: 0 2rpx 8rpx rgba(51, 126, 169, 0.2);
}

.primary-button:active {
  background-color: #2a6a8f;
  border-color: #2a6a8f;
  box-shadow: 0 1rpx 4rpx rgba(51, 126, 169, 0.15);
  transform: translateY(2rpx);
}

.card-title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
}

.parse-button {
  padding: 8rpx 16rpx;
  font-size: 14px;
  color: #3498db;
  background-color: transparent;
  border: 1px solid #3498db;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.parse-button:active {
  opacity: 0.8;
}
</style>
