<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'Notion',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="overflow-hidden pb-safe">
    <view class="flex flex-col items-center justify-center w-full">
      <view
        class="rounded-16px bg-white w-690rpx mt-12px py-4 flex flex-col justify-start items-center shadow-sm"
      >
        <wd-text
          text="Notion设置"
          size="18px"
          custom-class="!c-#333 ml-8 w-full mb-2 font-bold"
        ></wd-text>
        <wd-gap bg-color="#eee" height="1px" custom-class="w-90%"></wd-gap>

        <!-- Notion授权项 -->
        <view
          class="w-full h-auto py-3 flex flex-row justify-between items-center"
          hover-class="hover-item"
          @click="handleAuthClick"
        >
          <view class="ml-40rpx flex flex-col items-start justify-center">
            <wd-text text="Notion授权" size="16px" custom-class="!c-#333"></wd-text>
            <view class="flex flex-row items-center">
              <view
                class="i-mingcute:check-circle-line w-14px h-14px mr-1"
                :style="{ color: authStatusColor }"
              ></view>
              <wd-text :text="authStatus" size="12px" :color="authStatusColor"></wd-text>
            </view>
          </view>
          <view class="mr-40rpx flex flex-row items-center justify-center">
            <image class="w-24px h-24px" src="/static/images/arrow-right.png"></image>
          </view>
        </view>

        <wd-gap bg-color="#eee" height="1px" custom-class="w-90%"></wd-gap>

        <!-- 文章数据库项 -->
        <view
          class="w-full h-auto py-3 flex flex-row justify-between items-center"
          hover-class="hover-item"
          @click="handleArticleDbClick"
        >
          <view class="flex flex-row items-center">
            <view class="ml-40rpx i-solar:document-add-linear w-20px h-20px"></view>
            <view class="ml-3 flex flex-col items-start justify-start">
              <wd-text text="修改文章数据库" size="14px" custom-class="!c-#333"></wd-text>
            </view>
          </view>
          <view class="mr-40rpx flex flex-row items-center justify-center">
            <image class="w-24px h-24px" src="/static/images/arrow-right.png"></image>
          </view>
        </view>

        <wd-gap bg-color="#eee" height="1px" custom-class="w-90%"></wd-gap>

        <!-- 消息数据库项 -->
        <view
          class="w-full h-auto py-3 flex flex-row justify-between items-center"
          hover-class="hover-item"
          @click="handleMessageDbClick"
        >
          <view class="flex flex-row items-center">
            <view class="ml-40rpx i-solar:chat-line-outline w-20px h-20px"></view>
            <view class="ml-3 flex flex-col items-start justify-start">
              <wd-text text="修改消息数据库" size="14px" custom-class="!c-#333"></wd-text>
            </view>
          </view>
          <view class="mr-40rpx flex flex-row items-center justify-center">
            <image class="w-24px h-24px" src="/static/images/arrow-right.png"></image>
          </view>
        </view>
      </view>
      <view
        class="rounded-16px bg-white w-690rpx mt-4 py-4 flex flex-col justify-start items-center shadow-sm"
      >
        <wd-text
          text="剪藏数据"
          size="18px"
          custom-class="!c-#333 ml-8 w-full mb-2 font-bold"
        ></wd-text>
        <wd-gap bg-color="#eee" height="1px" custom-class="w-90%"></wd-gap>
        <wd-row custom-class="w-full mt-2">
          <wd-col :span="8">
            <stat-item
              :value="clipData.curMonthCount"
              label="本月剪藏"
              value-color="#337ea9"
              valueSize="22px"
            />
          </wd-col>
          <wd-col :span="8">
            <stat-item
              :value="clipData.curDayCount"
              label="今日剪藏"
              value-color="#337ea9"
              valueSize="22px"
            />
          </wd-col>
          <wd-col :span="8">
            <stat-item
              :value="clipData.totalCount"
              label="总剪藏数"
              value-color="#337ea9"
              valueSize="22px"
            />
          </wd-col>
        </wd-row>
      </view>

      <!-- 联系我们卡片 -->
      <view
        class="rounded-16px bg-white w-690rpx mt-4 py-4 flex flex-col justify-start items-center shadow-sm"
      >
        <wd-text
          text="联系开发者"
          size="18px"
          custom-class="!c-#333 ml-8 w-full mb-2 font-bold"
        ></wd-text>
        <wd-gap bg-color="#eee" height="1px" custom-class="w-90%"></wd-gap>
        <!-- 在线联系 -->
        <view class="w-full py-3 flex flex-row items-center">
          <view class="w-full flex flex-row items-center">
            <view class="ml-40rpx">
              <view class="c-#337ea9 w-24px h-24px i-solar:chat-dots-linear"></view>
            </view>
            <view class="ml-4 flex flex-col items-start justify-center">
              <wd-text text="微信号" size="14px" custom-class="!c-#333 font-medium"></wd-text>
              <wd-text text="@NotionHelper" size="13px" color="#666"></wd-text>
            </view>
          </view>
          <wd-button size="small" plain custom-class="mr-8" open-type="contact">在线联系</wd-button>
        </view>

        <wd-gap bg-color="#eee" height="1px" custom-class="w-90%"></wd-gap>
        <!-- 微信公众号 -->
        <view class="w-full py-3 flex flex-row items-center">
          <view class="ml-40rpx">
            <view class="c-#00DC6C w-24px h-24px i-solar:dialog-linear"></view>
          </view>
          <view class="ml-4 flex flex-col items-start justify-center">
            <wd-text text="公众号" size="14px" custom-class="!c-#333 font-medium"></wd-text>
            <wd-text text="@NotionHelper" size="13px" color="#666"></wd-text>
          </view>
        </view>

        <wd-gap bg-color="#eee" height="1px" custom-class="w-90%"></wd-gap>

        <!-- 小红书联系方式 -->
        <view class="w-full py-3 flex flex-row items-center">
          <view class="ml-40rpx">
            <wd-img width="24px" height="24px" src="/static/images/operate/redbook.png" />
          </view>
          <view class="ml-4 flex flex-col items-start justify-center">
            <wd-text text="小红书" size="14px" custom-class="!c-#333 font-medium"></wd-text>
            <wd-text text="@麦可" size="13px" color="#666"></wd-text>
          </view>
        </view>
        <wd-gap bg-color="#eee" height="1px" custom-class="w-90%"></wd-gap>
      </view>
    </view>
    <wd-gap height="16px"></wd-gap>
  </view>
</template>

<script lang="ts" setup>
import { computed, reactive } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import StatItem from '@/pages/about/components/stat-item.vue'
import { useUserStore } from '@/store'
import { useToast } from 'wot-design-uni'
import { getUserClipData } from '@/service/user/user'
import { extractErrorMessage } from '@/utils'

const toast = useToast()
const userStore = useUserStore()

// Notion授权状态从store中获取
const isAuthorized = computed(() => !!userStore.userInfo.notionAuth)
const authStatus = computed(() => (isAuthorized.value ? '已授权' : '未授权'))
const authStatusColor = computed(() => (isAuthorized.value ? '#337ea9' : '#909399'))

const clipData = reactive({
  curDayCount: 0,
  curMonthCount: 0,
  totalCount: 0,
})

// 标记是否已经在 onLoad 中加载过数据
const hasLoadedData = ref(false)

// 点击处理函数
const handleAuthClick = () => {
  uni.navigateTo({
    url: '/pages/notion/authorize/authorize',
  })
}

const handleArticleDbClick = () => {
  if (!isAuthorized.value) {
    toast.warning('请先完成授权')
    return
  }
  uni.navigateTo({
    url: '/pages/notion/articledatabase/articledatabase',
  })
}

const handleMessageDbClick = () => {
  if (!isAuthorized.value) {
    toast.warning('请先完成授权')
    return
  }
  uni.navigateTo({
    url: '/pages/notion/messagedatabase/messagedatabase',
  })
}
// 页面首次加载时获取数据
onLoad(() => {
  console.log('onLoad: 首次加载页面')
  // 标记已加载数据
  hasLoadedData.value = true

  // 无条件获取剪藏数据
  console.log('onLoad: 获取剪藏数据')
  fetchClipData()
})

// 页面显示时根据条件获取数据
onShow(() => {
  console.log('onShow: 页面显示')

  // 如果是首次加载，onLoad 已经处理过数据，不需要重复处理
  if (!hasLoadedData.value) {
    // 判断是否需要刷新剪藏数据
    // 只有在以下情况才刷新：
    // 1. 数据已过期
    // 2. 明确设置了需要刷新的标记
    if (userStore.isClipDataStale || userStore.needRefreshClipData) {
      console.log('onShow: 需要刷新剪藏数据')
      fetchClipData()
      // 重置刷新标记
      if (userStore.needRefreshClipData) {
        userStore.setNeedRefreshClipData(false)
      }
    } else {
      console.log('onShow: 不需要刷新剪藏数据，使用本地存储的数据')
    }
  } else {
    // 重置标记，下次 onShow 时正常检查
    hasLoadedData.value = false
  }
})

const fetchClipData = async () => {
  try {
    const res = await getUserClipData()
    if (res.success && res.data) {
      const vo = res.data
      clipData.curDayCount = vo.curDayCount || 0
      clipData.curMonthCount = vo.curMonthCount || 0
      clipData.totalCount = vo.totalCount || 0

      // 更新剪藏数据刷新时间
      userStore.updateClipDataRefreshTime()
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('获取剪藏数据失败:', errorMessage)
  }
}
</script>

<style lang="scss" scoped>
.hover-item {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>
