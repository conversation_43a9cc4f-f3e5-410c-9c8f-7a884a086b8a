<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'Obsidian配置',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="config-container">
    <!-- 配置状态卡片 -->
    <wd-card class="mb-4 shadow rounded-lg mx-[16rpx]">
      <view class="px-[16rpx] flex justify-between items-center">
        <view>
          <wd-text size="16px" :text="configStatusText" :color="configStatusColor" bold />
        </view>
        <view class="flex items-center">
          <view
            class="i-mingcute:check-circle-line w-[48rpx] h-[48rpx]"
            :style="{ color: configStatusColor }"
          ></view>
        </view>
      </view>
    </wd-card>

    <!-- 配置说明 -->
    <view class="px-[16px] w-690rpx text-center mb-4 font-size-[12px] c-#696969">
      Obsidian剪藏需要配置S3对象存储和页面属性，请按照以下步骤完成配置
    </view>

    <!-- 配置项列表 -->
    <wd-card class="mb-4 shadow rounded-lg mx-[16rpx]">
      <wd-cell-group>
        <!-- S3存储配置 -->
        <wd-cell
          title="S3存储配置"
          label="必需配置，用于存储文件和图片"
          :value="s3ConfigStatus"
          is-link
          @click="handleS3ConfigClick"
        >
          <template #icon>
            <view class="i-solar:cloud-storage-linear w-20px h-20px mr-2" style="color: #8B5CF6"></view>
          </template>
        </wd-cell>

        <!-- 文章页面属性配置 -->
        <wd-cell
          title="文章页面属性"
          label="配置文章在Obsidian中的属性字段"
          :value="articleConfigStatus"
          is-link
          @click="handleArticleConfigClick"
        >
          <template #icon>
            <view class="i-solar:document-add-linear w-20px h-20px mr-2" style="color: #8B5CF6"></view>
          </template>
        </wd-cell>

        <!-- 消息页面属性配置 -->
        <wd-cell
          title="消息页面属性"
          label="配置消息在Obsidian中的属性字段"
          :value="messageConfigStatus"
          is-link
          @click="handleMessageConfigClick"
        >
          <template #icon>
            <view class="i-solar:chat-line-outline w-20px h-20px mr-2" style="color: #8B5CF6"></view>
          </template>
        </wd-cell>

        <!-- 路径配置 -->
        <wd-cell
          title="保存路径配置"
          label="配置文章和资源的保存路径"
          :value="pathConfigStatus"
          is-link
          @click="handlePathConfigClick"
        >
          <template #icon>
            <view class="i-solar:folder-path-connect-linear w-20px h-20px mr-2" style="color: #8B5CF6"></view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </wd-card>

    <!-- 测试连接按钮 -->
    <view class="p-[32rpx]">
      <wd-button type="primary" block @click="testConnection" :loading="testing">
        测试配置连接
      </wd-button>
    </view>

    <!-- 配置说明卡片 -->
    <wd-card class="mb-4 shadow rounded-lg mx-[16rpx]">
      <view class="p-[16rpx]">
        <wd-text text="配置说明" size="16px" bold class="mb-2" />
        <view class="text-sm text-gray-600 space-y-2">
          <view>1. S3存储：用于存储剪藏的文件和图片，支持AWS S3、阿里云OSS等</view>
          <view>2. 页面属性：定义文章和消息在Obsidian中的元数据字段</view>
          <view>3. 保存路径：指定文件在Obsidian库中的存储位置</view>
          <view>4. 所有配置完成后，点击测试连接验证配置是否正确</view>
        </view>
      </view>
    </wd-card>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onShow } from 'vue'
import { useToast } from 'wot-design-uni'
import { useUserStore } from '@/store'
import { checkObsidianConfig, testS3Connection } from '@/service/obsidian/config'
import { extractErrorMessage } from '@/utils'

const toast = useToast()
const userStore = useUserStore()

// 配置状态
const configValid = ref(false)
const s3ConfigValid = ref(false)
const articleConfigValid = ref(false)
const messageConfigValid = ref(false)
const pathConfigValid = ref(false)
const testing = ref(false)

// 计算属性
const configStatusText = computed(() => {
  return configValid.value ? 'Obsidian配置有效' : 'Obsidian配置无效'
})

const configStatusColor = computed(() => {
  return configValid.value ? '#8B5CF6' : '#909399'
})

const s3ConfigStatus = computed(() => s3ConfigValid.value ? '已配置' : '未配置')
const articleConfigStatus = computed(() => articleConfigValid.value ? '已配置' : '未配置')
const messageConfigStatus = computed(() => messageConfigValid.value ? '已配置' : '未配置')
const pathConfigStatus = computed(() => pathConfigValid.value ? '已配置' : '未配置')

// 页面显示时检查配置状态
onShow(async () => {
  await checkConfigStatus()
})

// 检查配置状态
const checkConfigStatus = async () => {
  try {
    const { data } = await checkObsidianConfig()
    if (data) {
      configValid.value = data.isValid
      s3ConfigValid.value = data.s3Config
      articleConfigValid.value = data.articleConfig
      messageConfigValid.value = data.messageConfig
      pathConfigValid.value = true // 暂时默认为true，后续可以添加具体检查
      
      // 更新用户store中的配置状态
      userStore.userInfo.obsidianConfigValid = data.isValid
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('检查配置状态失败:', errorMessage)
  }
}

// 点击处理函数
const handleS3ConfigClick = () => {
  uni.navigateTo({
    url: '/pages/about/cloudpic/cloudpic',
  })
}

const handleArticleConfigClick = () => {
  uni.navigateTo({
    url: '/pages/about/obsidian/article-config/article-config',
  })
}

const handleMessageConfigClick = () => {
  uni.navigateTo({
    url: '/pages/about/obsidian/message-config/message-config',
  })
}

const handlePathConfigClick = () => {
  toast.info('路径配置功能开发中...')
}

// 测试连接
const testConnection = async () => {
  if (!s3ConfigValid.value) {
    toast.warning('请先配置S3存储')
    return
  }
  
  testing.value = true
  try {
    // 这里可以调用测试接口
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟测试
    toast.success('配置测试通过')
    await checkConfigStatus() // 重新检查状态
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    toast.error(`测试失败: ${errorMessage}`)
  } finally {
    testing.value = false
  }
}
</script>

<style lang="scss" scoped>
.config-container {
  padding: 16rpx;
}
</style>
