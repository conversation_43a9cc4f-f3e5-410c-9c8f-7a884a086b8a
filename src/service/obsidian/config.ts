import { http } from '@/utils/http'

/**
 * Obsidian配置检查结果
 */
export interface ObsidianConfigCheck {
  isValid: boolean
  s3Config: boolean
  articleConfig: boolean
  messageConfig: boolean
  message?: string
}

/**
 * Obsidian配置信息
 */
export interface ObsidianConfig {
  /**
   * 主键 id
   */
  id?: number

  /**
   * 用户id
   */
  unionId?: string

  /**
   * 文章属性配置
   */
  artInfo?: string

  /**
   * 消息属性配置
   */
  msgInfo?: string

  /**
   * s3配置id
   */
  s3Id?: number

  /**
   * 文章保存路径
   */
  saveRoot?: string

  /**
   * 资源保存路径
   */
  attRoot?: string

  /**
   * 创建时间戳
   */
  gmtCreate?: number

  /**
   * 更新时间戳
   */
  gmtUpdate?: number

  /**
   * 是否删除
   */
  deleted?: number
}

/**
 * 文章页面属性配置
 */
export interface ArticlePageConfig {
  category: string
  title: string
  url: string
  author: string
  tags: string
  origin: string
  remark: string
  createTime: string
  publishTime: string
}

/**
 * 消息页面属性配置
 */
export interface MessagePageConfig {
  category: string
  tags: string
  createTime: string
  type: string
}

/**
 * S3配置信息
 */
export interface S3Config {
  id?: number
  accessKey: string
  secretKey: string
  bucket: string
  region: string
  endpoint: string
}

/**
 * 检查Obsidian配置是否有效
 */
export const checkObsidianConfig = () => {
  return http.get<ObsidianConfigCheck>('/miniprogram/obsidian/checkConfig')
}

/**
 * 获取Obsidian配置
 */
export const getObsidianConfig = () => {
  return http.get<ObsidianConfig>('/miniprogram/obsidian/getConfig')
}

/**
 * 保存Obsidian配置
 */
export const saveObsidianConfig = (config: ObsidianConfig) => {
  return http.post<boolean>('/miniprogram/obsidian/saveConfig', config)
}

/**
 * 获取文章页面属性配置
 */
export const getArticlePageConfig = () => {
  return http.get<ArticlePageConfig>('/miniprogram/obsidian/getArticlePageConfig')
}

/**
 * 保存文章页面属性配置
 */
export const saveArticlePageConfig = (config: ArticlePageConfig) => {
  return http.post<boolean>('/miniprogram/obsidian/saveArticlePageConfig', config)
}

/**
 * 获取消息页面属性配置
 */
export const getMessagePageConfig = () => {
  return http.get<MessagePageConfig>('/miniprogram/obsidian/getMessagePageConfig')
}

/**
 * 保存消息页面属性配置
 */
export const saveMessagePageConfig = (config: MessagePageConfig) => {
  return http.post<boolean>('/miniprogram/obsidian/saveMessagePageConfig', config)
}

/**
 * 获取S3配置
 */
export const getS3Config = () => {
  return http.get<S3Config>('/miniprogram/obsidian/getS3Config')
}

/**
 * 保存S3配置
 */
export const saveS3Config = (config: S3Config) => {
  return http.post<boolean>('/miniprogram/obsidian/saveS3Config', config)
}

/**
 * 测试S3连接
 */
export const testS3Connection = (config: S3Config) => {
  return http.post<boolean>('/miniprogram/obsidian/testS3Connection', config)
}
