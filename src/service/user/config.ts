import { http } from '@/utils/http'

export interface CustomConfig {
  alwaysUsePicCloud: boolean
  alwaysUseCloudinary: boolean
  supportAllSite: boolean
  noIcon: boolean
  noCover: boolean
  quickClipAsEnterPage: boolean
  toObsidian: boolean
}
/**
 * 获取配置开关
 */
export const getCustomConfig = () => {
  return http.get<CustomConfig>('/miniprogram/getCustomConfig')
}

/**
 * 保存配置
 */
export const postCustomConfig = (body: CustomConfig) => {
  return http.post<boolean>('/miniprogram/postCustomConfig', body)
}
