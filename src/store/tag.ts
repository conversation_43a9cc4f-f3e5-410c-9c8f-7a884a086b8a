import { defineStore } from 'pinia'

// 标签存储，使用LRU队列管理历史标签
export const useTagStore = defineStore('tag', {
  state: () => ({
    // 历史标签队列，最多保存30个
    historyTags: [] as string[],
    // 队列最大容量
    maxCapacity: 30,
  }),

  actions: {
    // 初始化标签，从本地存储加载
    initTags() {
      try {
        const storedTags = uni.getStorageSync('tags')
        if (storedTags) {
          this.historyTags = JSON.parse(storedTags)
        }
      } catch (e) {
        console.error('加载历史标签失败:', e)
        this.historyTags = []
      }
    },

    // 保存标签到本地存储
    saveTags() {
      try {
        uni.setStorageSync('tags', JSON.stringify(this.historyTags))
      } catch (e) {
        console.error('保存历史标签失败:', e)
      }
    },

    // 添加新标签到队列最前面
    addTag(tag: string) {
      // 如果标签已存在，先移除
      this.removeTag(tag)

      // 添加到队列最前面
      this.historyTags.unshift(tag)

      // 如果超出容量，移除末尾元素
      if (this.historyTags.length > this.maxCapacity) {
        this.historyTags.pop()
      }

      // 保存到本地存储
      this.saveTags()
    },

    // 使用标签（选择已有标签），将其移到队列最前面
    useTag(tag: string) {
      // 如果标签不存在，直接返回
      const index = this.historyTags.indexOf(tag)
      if (index === -1) return

      // 从当前位置移除
      this.historyTags.splice(index, 1)

      // 添加到队列最前面
      this.historyTags.unshift(tag)

      // 保存到本地存储
      this.saveTags()
    },

    // 从队列中移除指定标签
    removeTag(tag: string) {
      const index = this.historyTags.indexOf(tag)
      if (index !== -1) {
        this.historyTags.splice(index, 1)
        this.saveTags()
      }
    },

    // 根据索引删除标签
    removeTagByIndex(index: number) {
      if (index >= 0 && index < this.historyTags.length) {
        this.historyTags.splice(index, 1)
        this.saveTags()
      }
    },

    // 获取所有历史标签
    getAllTags() {
      return this.historyTags
    },

    // 清空所有历史标签
    clearAllTags() {
      this.historyTags = []
      this.saveTags()
    },
  },
})
