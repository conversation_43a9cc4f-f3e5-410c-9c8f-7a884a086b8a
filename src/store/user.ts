import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { IUserInfo } from '@/typings'

const initState = {
  nickname: '游客用户',
  avatar:
    'https://res.cloudinary.com/dwal1nlws/image/upload/v1740285825/wexin/mp/461865305a33adc8d4dbeae75ed08502.png',
}

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>({ ...initState })

    // 添加数据刷新标记
    const needRefreshUserInfo = ref(false)
    const needRefreshPointsData = ref(false)
    const needRefreshClipData = ref(false) // 新增：剪藏数据刷新标记

    // 记录最后一次数据刷新时间
    const lastUserInfoRefreshTime = ref(0)
    const lastPointsDataRefreshTime = ref(0)
    const lastClipDataRefreshTime = ref(0) // 新增：剪藏数据刷新时间

    // 缓存积分数据
    const cachedPointsData = ref({
      sum: 0,
      availableSum: 0,
      shareCount: 0,
    })

    // 数据刷新时间间隔（毫秒），默认5分钟
    const DATA_REFRESH_INTERVAL = 5 * 60 * 1000

    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = val
      // 更新最后刷新时间
      lastUserInfoRefreshTime.value = Date.now()
    }

    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }

    // 一般没有reset需求，不需要的可以删除
    const reset = () => {
      userInfo.value = { ...initState }
    }

    const isLogined = computed(() => !!userInfo.value.token)

    const setToken = (token: string) => {
      userInfo.value.token = token
    }

    // 设置需要刷新用户信息的标记
    const setNeedRefreshUserInfo = (val: boolean) => {
      needRefreshUserInfo.value = val
    }

    // 设置需要刷新积分数据的标记
    const setNeedRefreshPointsData = (val: boolean) => {
      needRefreshPointsData.value = val
    }

    // 设置需要刷新剪藏数据的标记
    const setNeedRefreshClipData = (val: boolean) => {
      needRefreshClipData.value = val
    }

    // 更新用户信息刷新时间
    const updateUserInfoRefreshTime = () => {
      lastUserInfoRefreshTime.value = Date.now()
    }

    // 更新积分数据刷新时间
    const updatePointsDataRefreshTime = () => {
      lastPointsDataRefreshTime.value = Date.now()
    }

    // 更新缓存的积分数据
    const updateCachedPointsData = (data: {
      sum: number
      availableSum: number
      shareCount: number
    }) => {
      cachedPointsData.value = { ...data }
      // 同时更新刷新时间
      lastPointsDataRefreshTime.value = Date.now()
    }

    // 获取缓存的积分数据
    const getCachedPointsData = () => {
      return cachedPointsData.value
    }

    // 更新剪藏数据刷新时间
    const updateClipDataRefreshTime = () => {
      lastClipDataRefreshTime.value = Date.now()
    }

    // 检查用户信息是否需要刷新（基于时间间隔）
    const isUserInfoStale = computed(() => {
      return Date.now() - lastUserInfoRefreshTime.value > DATA_REFRESH_INTERVAL
    })

    // 检查积分数据是否需要刷新（基于时间间隔）
    const isPointsDataStale = computed(() => {
      return Date.now() - lastPointsDataRefreshTime.value > DATA_REFRESH_INTERVAL
    })

    // 检查剪藏数据是否需要刷新（基于时间间隔）
    const isClipDataStale = computed(() => {
      return Date.now() - lastClipDataRefreshTime.value > DATA_REFRESH_INTERVAL
    })

    return {
      userInfo,
      needRefreshUserInfo,
      needRefreshPointsData,
      needRefreshClipData,
      lastUserInfoRefreshTime,
      lastPointsDataRefreshTime,
      lastClipDataRefreshTime,
      cachedPointsData,
      setUserInfo,
      clearUserInfo,
      isLogined,
      reset,
      setToken,
      setNeedRefreshUserInfo,
      setNeedRefreshPointsData,
      setNeedRefreshClipData,
      updateUserInfoRefreshTime,
      updatePointsDataRefreshTime,
      updateCachedPointsData,
      getCachedPointsData,
      updateClipDataRefreshTime,
      isUserInfoStale,
      isPointsDataStale,
      isClipDataStale,
    }
  },
  {
    persist: true,
  },
)
