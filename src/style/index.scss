// @import './iconfont.css';

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}

:root,
page {
  // 修改按主题色 - Obsidian紫色
  --wot-color-theme: #8B5CF6;

  // 修改按钮背景色
  // --wot-button-primary-bg-color: green;
}

.tr-shadow {
  box-shadow: #edeef1 0px 1px 7px 0px;
}

.material-shadow {
  box-shadow:
    0 3px 1px -2px rgb(0 0 0 / 20%),
    0 2px 2px 0 rgb(0 0 0 / 14%),
    0 1px 5px 0 rgb(0 0 0 / 12%);
}
