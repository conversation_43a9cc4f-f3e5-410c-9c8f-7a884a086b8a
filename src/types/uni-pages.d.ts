/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/about/about" |
       "/pages/login/index" |
       "/pages/notion/notion" |
       "/pages/about/assistant/assistant" |
       "/pages/about/cloudpic/cloudpic" |
       "/pages/about/feedback/feedback" |
       "/pages/about/labor/labor" |
       "/pages/about/member/member" |
       "/pages/about/obsidian/obsidian" |
       "/pages/about/personal/personal" |
       "/pages/about/points/points" |
       "/pages/about/privacy/privacy" |
       "/pages/about/profile/profile" |
       "/pages/index/article/article" |
       "/pages/index/news/news" |
       "/pages/index/quickclip/quickclip" |
       "/pages/notion/articledatabase/articledatabase" |
       "/pages/notion/authorize/authorize" |
       "/pages/notion/messagedatabase/messagedatabase" |
       "/pages/about/cloudpic/cloudinary/cloudinary" |
       "/pages/about/cloudpic/enpower/enpower" |
       "/pages/about/member/notices/notices" |
       "/pages/about/member/rights/rights" |
       "/pages/about/obsidian/article-config/article-config" |
       "/pages/about/obsidian/message-config/message-config" |
       "/pages/index/article/remark-editor/remark-editor" |
       "/pages/index/article/tag-manager/tag-manager";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/notion/notion" | "/pages/index/index" | "/pages/about/about"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
