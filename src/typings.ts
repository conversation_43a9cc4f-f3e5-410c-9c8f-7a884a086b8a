// 全局要用的类型放到这里

export type IResData<T> = {
  code: number
  msg: string
  data: T
  success: boolean
  requestId: string
}

// uni.uploadFile文件上传参数
export type IUniUploadFileOptions = {
  file?: File
  files?: UniApp.UploadFileOptionFiles[]
  filePath?: string
  name?: string
  formData?: any
}

export type IUserInfo = {
  nickname?: string
  avatar?: string
  unionId?: string
  openid?: string
  token?: string
  subUserId?: string
  createTime?: string
  endTime?: string
  notionAuth?: boolean // 保留兼容性，后续可能移除
  obsidianConfigValid?: boolean // 新增：Obsidian配置是否有效
  userType?: string
  memberNo?: number
  email?: string
  msgCount?: number
  articleCount?: number
  shareCount?: number
  mainUserVo?: {
    nickname?: string
    avatar?: string
    unionId?: string
    openid?: string
    token?: string
    subUserId?: string
    createTime?: string
    endTime?: string
    notionAuth?: boolean // 保留兼容性，后续可能移除
    obsidianConfigValid?: boolean // 新增：Obsidian配置是否有效
    userType?: string
    memberNo?: number
    email?: string
    msgCount?: number
    articleCount?: number
    shareCount?: number
  }
}

export enum TestEnum {
  A = 'a',
  B = 'b',
}
