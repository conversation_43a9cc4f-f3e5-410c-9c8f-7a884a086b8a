import { CustomRequestOptions } from '@/interceptors/request'
import type { IResData } from '@/typings'
import { isLoginInvalidCode } from '@/constants/errorCode'
import { useUserStore } from '@/store'
import { getEnvBaseUrl } from '@/utils'

export const http = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.request({
      ...options,
      // 响应成功
      success(res) {
        console.log('请求成功:', options.url, res.statusCode, res.data)
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 2.1 提取核心数据 res.data
          // 确保返回的数据符合 IResData 结构
          try {
            const responseData = res.data as IResData<T>
            // 验证关键字段是否存在
            if (responseData.code === undefined) {
              console.warn('响应数据缺少 code 字段')
            }

            // 检查是否是登录失效的错误码
            if (responseData.code !== undefined && isLoginInvalidCode(responseData.code)) {
              console.warn('登录信息已失效，错误码:', responseData.code)
              // 处理登录失效情况
              handleLoginInvalid(responseData.msg || '登录已失效，请重新登录')
              reject(responseData)
              return
            }

            resolve(responseData)
          } catch (error) {
            console.error('解析响应数据失败:', error)
            reject(new Error('响应数据格式不正确'))
          }
        } else if (res.statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          // userStore.clearUserInfo()
          // uni.navigateTo({ url: '/pages/login/login' })
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          let errorMsg = '请求错误'
          try {
            if (res.data && typeof res.data === 'object' && 'msg' in res.data) {
              errorMsg = res.data.msg || errorMsg
            }
          } catch (e) {
            console.error('获取错误信息失败:', e)
          }
          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: errorMsg,
            })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        console.error('请求失败:', options.url, err)
        // 网络错误
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @returns
 */
export const httpGet = <T>(url: string, query?: Record<string, any>) => {
  return http<T>({
    url,
    query,
    method: 'GET',
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @returns
 */
export const httpPost = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
  })
}

/**
 * 处理登录失效的情况
 * @param errorMsg 错误信息
 */
const handleLoginInvalid = (errorMsg: string) => {
  // 获取用户存储
  const userStore = useUserStore()

  // 清除用户信息
  userStore.clearUserInfo()

  // 显示错误提示
  uni.showToast({
    title: errorMsg,
    icon: 'none',
    duration: 2000,
  })

  // 延迟跳转到登录页面，给用户时间看到提示
  setTimeout(() => {
    // 获取当前页面路径，用于登录后返回
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = `/${currentPage.route}`
    const currentParams = currentPage.options

    // 构建当前页面的完整URL（包含参数）
    let redirectUrl = currentRoute
    const paramKeys = Object.keys(currentParams)
    if (paramKeys.length > 0) {
      redirectUrl += '?'
      paramKeys.forEach((key, index) => {
        redirectUrl += `${key}=${currentParams[key]}`
        if (index < paramKeys.length - 1) {
          redirectUrl += '&'
        }
      })
    }

    // 跳转到登录页面，并传递返回地址
    uni.navigateTo({
      url: `/pages/login/index?redirect=${encodeURIComponent(redirectUrl)}`,
    })
  }, 1500)
}

/**
 * 文件上传请求
 * @param url 后台地址
 * @param filePath 文件路径
 * @param formData 额外的表单数据
 * @returns Promise
 */
export const httpUploadFile = <T>(
  url: string,
  filePath: string,
  formData?: Record<string, any>,
) => {
  // 获取基础URL
  const baseUrl = getEnvBaseUrl()

  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : baseUrl + url

  // 返回Promise
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.uploadFile({
      url: fullUrl,
      filePath,
      name: 'file', // 文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容
      formData,
      success: (res) => {
        console.log('上传成功:', url, res.statusCode, res.data)
        // 状态码 2xx
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            // 解析返回的JSON数据
            const responseData = JSON.parse(res.data) as IResData<T>

            // 验证关键字段是否存在
            if (responseData.code === undefined) {
              console.warn('响应数据缺少 code 字段')
            }

            // 检查是否是登录失效的错误码
            if (responseData.code !== undefined && isLoginInvalidCode(responseData.code)) {
              console.warn('登录信息已失效，错误码:', responseData.code)
              // 处理登录失效情况
              handleLoginInvalid(responseData.msg || '登录已失效，请重新登录')
              reject(responseData)
              return
            }

            resolve(responseData)
          } catch (error) {
            console.error('解析响应数据失败:', error)
            reject(new Error('响应数据格式不正确'))
          }
        } else if (res.statusCode === 401) {
          // 401错误 -> 未授权
          reject(res)
        } else {
          // 其他错误
          let errorMsg = '上传失败'
          try {
            const data = JSON.parse(res.data)
            if (data && typeof data === 'object' && 'msg' in data) {
              errorMsg = data.msg || errorMsg
            }
          } catch (e) {
            console.error('获取错误信息失败:', e)
          }

          uni.showToast({
            icon: 'none',
            title: errorMsg,
          })
          reject(res)
        }
      },
      fail: (err) => {
        console.error('上传失败:', url, err)
        // 网络错误
        uni.showToast({
          icon: 'none',
          title: '网络错误，请检查网络连接',
        })
        reject(err)
      },
    })
  })
}

http.get = httpGet
http.post = httpPost
http.uploadFile = httpUploadFile
